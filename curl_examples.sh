#!/bin/bash

# 批量头像分配接口测试脚本
# 使用方法: ./curl_examples.sh [user_id]

BASE_URL="http://localhost:8000"
USER_ID="${1:-zhangzheng51}"  # 默认用户ID，可通过参数覆盖

echo "🚀 批量头像分配接口测试"
echo "用户ID: $USER_ID"
echo "服务地址: $BASE_URL"
echo ""

# 1. 获取头像资源池
echo "📋 1. 获取头像资源池配置"
curl -X GET "$BASE_URL/humanrelation/avatar_pools" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# 2. 试运行模式（预览）
echo "🔍 2. 试运行模式（预览分配结果）"
curl -X POST "$BASE_URL/humanrelation/batch_assign_avatars" \
  -H "Content-Type: application/json" \
  -d "{
    \"user_id\": \"$USER_ID\",
    \"limit\": 10,
    \"dry_run\": true
  }" | jq '.'
echo ""

# 3. 询问是否执行实际更新
echo "❓ 是否执行实际更新？(输入 'yes' 继续，其他任意键取消)"
read -r confirm

if [ "$confirm" = "yes" ]; then
    echo "💾 3. 实际执行模式"
    curl -X POST "$BASE_URL/humanrelation/batch_assign_avatars" \
      -H "Content-Type: application/json" \
      -d "{
        \"user_id\": \"$USER_ID\",
        \"limit\": 10,
        \"dry_run\": false
      }" | jq '.'
    echo ""
    echo "✅ 实际更新完成！"
else
    echo "❌ 取消实际更新"
fi

echo ""
echo "🎉 测试完成！"

# 使用说明
echo ""
echo "📖 其他使用示例："
echo ""
echo "# 处理所有用户的数据（试运行）"
echo "curl -X POST \"$BASE_URL/humanrelation/batch_assign_avatars\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{"
echo "    \"user_id\": \"\","
echo "    \"limit\": 50,"
echo "    \"dry_run\": true"
echo "  }'"
echo ""
echo "# 批量处理更多数据（实际执行）"
echo "curl -X POST \"$BASE_URL/humanrelation/batch_assign_avatars\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{"
echo "    \"user_id\": \"$USER_ID\","
echo "    \"limit\": 100,"
echo "    \"dry_run\": false"
echo "  }'"
