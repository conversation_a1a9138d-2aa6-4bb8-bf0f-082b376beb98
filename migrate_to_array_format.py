#!/usr/bin/env python3
"""
数据迁移脚本：将管道符分隔的字段转换为数组格式
"""

import os
import sys
import json

sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

from service.mysql_person_service import get_all_persons_mysql, update_person_mysql
from utils.logger import logger

def convert_to_array_format(value, field_name: str) -> list:
    """
    将字段值转换为数组格式
    
    Args:
        value: 字段值
        field_name: 字段名称
        
    Returns:
        list: 转换后的数组
    """
    if not value:
        return []
    
    # 如果已经是列表，直接返回
    if isinstance(value, list):
        return [str(item).strip() for item in value if str(item).strip()]
    
    # 转换为字符串处理
    value_str = str(value).strip()
    if not value_str:
        return []
    
    # 检查是否包含管道符
    if "|" in value_str:
        # 按管道符分割
        parts = [part.strip() for part in value_str.split("|") if part.strip()]
        logger.info(f"[迁移] {field_name}: 管道符格式 '{value_str}' -> 数组 {parts}")
        return parts
    
    # 检查是否为明显的列表数据（包含分隔符但不是描述性文本）
    list_separators = ["，", "；", "、", ",", ";"]
    descriptive_indicators = [
        "很", "了", "的", "是", "在", "我", "他", "她", "我们", "他们",
        "觉得", "感觉", "认为", "喜欢", "不喜欢", "但是", "不过", "然而",
        "。", "！", "？", "...", "…"
    ]
    
    has_separators = any(sep in value_str for sep in list_separators)
    has_descriptive = any(indicator in value_str for indicator in descriptive_indicators)
    
    if has_separators and not has_descriptive:
        # 可能是列表数据，尝试分割
        for sep in list_separators:
            if sep in value_str:
                parts = [part.strip() for part in value_str.split(sep) if part.strip()]
                if len(parts) > 1:
                    logger.info(f"[迁移] {field_name}: 分隔符格式 '{value_str}' -> 数组 {parts}")
                    return parts
    
    # 默认作为单个元素
    logger.info(f"[迁移] {field_name}: 单个值 '{value_str}' -> 数组 ['{value_str}']")
    return [value_str]

def should_convert_field(field_name: str, value) -> bool:
    """
    判断字段是否需要转换为数组格式
    
    Args:
        field_name: 字段名称
        value: 字段值
        
    Returns:
        bool: 是否需要转换
    """
    # 跳过基本信息等嵌套结构
    if field_name == "基本信息":
        return False
    
    # 跳过空值
    if not value:
        return False
    
    # 如果已经是列表，不需要转换
    if isinstance(value, list):
        return False
    
    # 跳过明显的单值字段
    single_value_fields = ["性别", "年龄", "职业", "关系", "当前城市", "家乡"]
    if field_name in single_value_fields:
        return False
    
    # 转换为字符串检查
    value_str = str(value).strip()
    if len(value_str) < 3:
        return False
    
    return True

def convert_key_attributes(key_attributes: dict) -> dict:
    """
    转换key_attributes中的字段为数组格式
    
    Args:
        key_attributes: 原始属性字典
        
    Returns:
        dict: 转换后的属性字典
    """
    if not isinstance(key_attributes, dict):
        return key_attributes
    
    result = {}
    
    def process_nested_dict(data, path=""):
        if isinstance(data, dict):
            processed = {}
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                
                if isinstance(value, dict):
                    # 递归处理嵌套字典
                    processed[key] = process_nested_dict(value, current_path)
                elif should_convert_field(key, value):
                    # 需要转换的字段
                    converted_array = convert_to_array_format(value, key)
                    processed[key] = converted_array
                else:
                    # 不需要转换的字段，保持原样
                    processed[key] = value
            return processed
        else:
            return data
    
    return process_nested_dict(key_attributes)

def migrate_person_data(user_id: str, person_id: str, person_data: dict) -> bool:
    """
    迁移单个人员的数据
    
    Args:
        user_id: 用户ID
        person_id: 人员ID
        person_data: 人员数据
        
    Returns:
        bool: 是否有数据被迁移
    """
    key_attributes = person_data.get("key_attributes", {})
    if not key_attributes:
        return False
    
    # 转换key_attributes
    converted_attributes = convert_key_attributes(key_attributes)
    
    # 检查是否有变化
    if converted_attributes == key_attributes:
        return False
    
    # 更新数据库
    try:
        result = update_person_mysql(
            user_id=user_id,
            person_id=person_id,
            key_attributes=converted_attributes
        )
        if result.get("result") == "success":
            logger.info(f"成功迁移人员 {person_data.get('canonical_name', 'Unknown')} 的数据")
            return True
        else:
            logger.error(f"迁移人员数据失败: {result}")
            return False
    except Exception as e:
        logger.error(f"迁移人员数据异常: {e}")
        return False

def migrate_user_data(user_id: str, dry_run: bool = True) -> dict:
    """
    迁移指定用户的所有数据
    
    Args:
        user_id: 用户ID
        dry_run: 是否为试运行模式
        
    Returns:
        dict: 迁移结果统计
    """
    logger.info(f"开始迁移用户 {user_id} 的数据，模式: {'试运行' if dry_run else '实际执行'}")
    
    # 获取用户的所有人员数据
    persons_result = get_all_persons_mysql(user_id=user_id, limit=1000, offset=0)
    if persons_result.get("result") != "success":
        logger.error(f"获取用户数据失败: {persons_result}")
        return {"result": "error", "reason": "获取用户数据失败"}
    
    persons = persons_result.get("persons", [])
    logger.info(f"找到 {len(persons)} 个人员记录")
    
    # 统计信息
    total_count = len(persons)
    migrated_count = 0
    error_count = 0
    details = []
    
    for person in persons:
        person_id = person.get("person_id")
        canonical_name = person.get("canonical_name", "Unknown")
        
        try:
            if dry_run:
                # 试运行模式：只检查不迁移
                key_attributes = person.get("key_attributes", {})
                converted_attributes = convert_key_attributes(key_attributes)
                
                if converted_attributes != key_attributes:
                    migrated_count += 1
                    details.append({
                        "person_id": person_id,
                        "canonical_name": canonical_name,
                        "status": "需要迁移",
                        "changes": "字段格式转换为数组"
                    })
            else:
                # 实际执行模式
                if migrate_person_data(user_id, person_id, person):
                    migrated_count += 1
                    details.append({
                        "person_id": person_id,
                        "canonical_name": canonical_name,
                        "status": "迁移成功"
                    })
                    
        except Exception as e:
            error_count += 1
            logger.error(f"处理人员 {canonical_name} 时出错: {e}")
            details.append({
                "person_id": person_id,
                "canonical_name": canonical_name,
                "status": "处理失败",
                "error": str(e)
            })
    
    result = {
        "result": "success",
        "mode": "试运行" if dry_run else "实际执行",
        "total_count": total_count,
        "migrated_count": migrated_count,
        "error_count": error_count,
        "details": details[:20]  # 只返回前20个详情
    }
    
    if len(details) > 20:
        result["total_details"] = len(details)
        result["showing_first"] = 20
    
    logger.info(f"迁移完成 - 总数: {total_count}, 迁移: {migrated_count}, 错误: {error_count}")
    return result

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="迁移数据到数组格式")
    parser.add_argument("user_id", help="用户ID")
    parser.add_argument("--execute", action="store_true", help="实际执行迁移（默认为试运行）")
    
    args = parser.parse_args()
    
    dry_run = not args.execute
    
    if not dry_run:
        confirm = input(f"⚠️  确认要迁移用户 {args.user_id} 的数据吗？这将修改数据库！(输入 'YES' 确认): ")
        if confirm != "YES":
            print("❌ 操作已取消")
            return
    
    result = migrate_user_data(args.user_id, dry_run=dry_run)
    
    print(f"\n🎯 迁移结果:")
    print(f"   模式: {result['mode']}")
    print(f"   总记录数: {result['total_count']}")
    print(f"   需要/已迁移: {result['migrated_count']}")
    print(f"   错误数: {result['error_count']}")
    
    if result['details']:
        print(f"\n📋 详细信息:")
        for detail in result['details'][:10]:  # 只显示前10个
            name = detail['canonical_name']
            status = detail['status']
            print(f"   • {name}: {status}")

if __name__ == "__main__":
    main()
