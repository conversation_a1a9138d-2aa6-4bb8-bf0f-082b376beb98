#!/usr/bin/env python3
"""
修复被错误格式化的数据
将类似 "去北京旅游了|挺好" 这样的错误格式化数据恢复为原始格式
"""

import os
import sys
import json

sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

from service.mysql_person_service import get_all_persons_mysql, update_person_mysql
from utils.logger import logger

def should_fix_field_value(field_name: str, value: str) -> tuple[bool, str]:
    """
    判断字段值是否需要修复，并返回修复后的值
    
    Args:
        field_name: 字段名
        value: 字段值
        
    Returns:
        tuple: (是否需要修复, 修复后的值)
    """
    if not isinstance(value, str) or "|" not in value:
        return False, value
    
    # 检查是否是被错误格式化的描述性文本
    parts = value.split("|")
    
    # 如果只有2个部分，且看起来像是被错误分割的句子
    if len(parts) == 2:
        part1, part2 = parts[0].strip(), parts[1].strip()
        
        # 常见的错误格式化模式
        error_patterns = [
            # "去北京旅游了|挺好" -> "去北京旅游了，挺好"
            (lambda p1, p2: p1.endswith(("了", "过", "的")) and len(p2) <= 10 and not any(c in p2 for c in "，。；"), 
             lambda p1, p2: f"{p1}，{p2}"),
            
            # "工作很忙|但是很充实" -> "工作很忙，但是很充实"  
            (lambda p1, p2: p2.startswith(("但是", "不过", "然而", "可是")) and len(p1) <= 20,
             lambda p1, p2: f"{p1}，{p2}"),
             
            # "我很喜欢|觉得很好" -> "我很喜欢，觉得很好"
            (lambda p1, p2: len(p1) <= 15 and len(p2) <= 15 and not any(c in p1+p2 for c in "，。；"),
             lambda p1, p2: f"{p1}，{p2}"),
        ]
        
        for pattern_check, pattern_fix in error_patterns:
            if pattern_check(part1, part2):
                fixed_value = pattern_fix(part1, part2)
                logger.info(f"检测到错误格式化: '{value}' -> '{fixed_value}'")
                return True, fixed_value
    
    return False, value

def fix_person_data(user_id: str, person_id: str, person_data: dict) -> bool:
    """
    修复单个人员的数据
    
    Args:
        user_id: 用户ID
        person_id: 人员ID  
        person_data: 人员数据
        
    Returns:
        bool: 是否有数据被修复
    """
    key_attributes = person_data.get("key_attributes", {})
    if not key_attributes:
        return False
    
    fixed_attributes = {}
    has_fixes = False
    
    # 递归检查所有字段
    def fix_nested_dict(data, path=""):
        nonlocal has_fixes
        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                if isinstance(value, str):
                    needs_fix, fixed_value = should_fix_field_value(key, value)
                    if needs_fix:
                        result[key] = fixed_value
                        has_fixes = True
                        logger.info(f"修复字段 {current_path}: '{value}' -> '{fixed_value}'")
                    else:
                        result[key] = value
                elif isinstance(value, (dict, list)):
                    result[key] = fix_nested_dict(value, current_path)
                else:
                    result[key] = value
            return result
        elif isinstance(data, list):
            return [fix_nested_dict(item, path) for item in data]
        else:
            return data
    
    fixed_attributes = fix_nested_dict(key_attributes)
    
    if has_fixes:
        # 更新数据库
        try:
            result = update_person_mysql(
                user_id=user_id,
                person_id=person_id, 
                key_attributes=fixed_attributes
            )
            if result.get("result") == "success":
                logger.info(f"成功修复人员 {person_data.get('canonical_name', 'Unknown')} 的数据")
                return True
            else:
                logger.error(f"修复人员数据失败: {result}")
                return False
        except Exception as e:
            logger.error(f"修复人员数据异常: {e}")
            return False
    
    return False

def fix_user_data(user_id: str, dry_run: bool = True) -> dict:
    """
    修复指定用户的所有数据
    
    Args:
        user_id: 用户ID
        dry_run: 是否为试运行模式
        
    Returns:
        dict: 修复结果统计
    """
    logger.info(f"开始修复用户 {user_id} 的数据，模式: {'试运行' if dry_run else '实际执行'}")
    
    # 获取用户的所有人员数据
    persons_result = get_all_persons_mysql(user_id=user_id, limit=1000, offset=0)
    if persons_result.get("result") != "success":
        logger.error(f"获取用户数据失败: {persons_result}")
        return {"result": "error", "reason": "获取用户数据失败"}
    
    persons = persons_result.get("persons", [])
    logger.info(f"找到 {len(persons)} 个人员记录")
    
    # 统计信息
    total_count = len(persons)
    fixed_count = 0
    error_count = 0
    details = []
    
    for person in persons:
        person_id = person.get("person_id")
        canonical_name = person.get("canonical_name", "Unknown")
        
        try:
            if dry_run:
                # 试运行模式：只检查不修复
                key_attributes = person.get("key_attributes", {})
                has_issues = False
                
                def check_nested_dict(data, path=""):
                    nonlocal has_issues
                    if isinstance(data, dict):
                        for key, value in data.items():
                            current_path = f"{path}.{key}" if path else key
                            if isinstance(value, str):
                                needs_fix, _ = should_fix_field_value(key, value)
                                if needs_fix:
                                    has_issues = True
                                    details.append({
                                        "person_id": person_id,
                                        "canonical_name": canonical_name,
                                        "field": current_path,
                                        "current_value": value,
                                        "status": "需要修复"
                                    })
                            elif isinstance(value, (dict, list)):
                                check_nested_dict(value, current_path)
                
                check_nested_dict(key_attributes)
                if has_issues:
                    fixed_count += 1
                    
            else:
                # 实际执行模式
                if fix_person_data(user_id, person_id, person):
                    fixed_count += 1
                    details.append({
                        "person_id": person_id,
                        "canonical_name": canonical_name,
                        "status": "修复成功"
                    })
                    
        except Exception as e:
            error_count += 1
            logger.error(f"处理人员 {canonical_name} 时出错: {e}")
            details.append({
                "person_id": person_id,
                "canonical_name": canonical_name,
                "status": "处理失败",
                "error": str(e)
            })
    
    result = {
        "result": "success",
        "mode": "试运行" if dry_run else "实际执行",
        "total_count": total_count,
        "fixed_count": fixed_count,
        "error_count": error_count,
        "details": details[:20]  # 只返回前20个详情
    }
    
    if len(details) > 20:
        result["total_details"] = len(details)
        result["showing_first"] = 20
    
    logger.info(f"修复完成 - 总数: {total_count}, 修复: {fixed_count}, 错误: {error_count}")
    return result

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="修复被错误格式化的数据")
    parser.add_argument("user_id", help="用户ID")
    parser.add_argument("--execute", action="store_true", help="实际执行修复（默认为试运行）")
    
    args = parser.parse_args()
    
    dry_run = not args.execute
    
    if not dry_run:
        confirm = input(f"⚠️  确认要修复用户 {args.user_id} 的数据吗？这将修改数据库！(输入 'YES' 确认): ")
        if confirm != "YES":
            print("❌ 操作已取消")
            return
    
    result = fix_user_data(args.user_id, dry_run=dry_run)
    
    print(f"\n🎯 修复结果:")
    print(f"   模式: {result['mode']}")
    print(f"   总记录数: {result['total_count']}")
    print(f"   需要/已修复: {result['fixed_count']}")
    print(f"   错误数: {result['error_count']}")
    
    if result['details']:
        print(f"\n📋 详细信息:")
        for detail in result['details'][:10]:  # 只显示前10个
            name = detail['canonical_name']
            status = detail['status']
            if 'field' in detail:
                field = detail['field']
                value = detail['current_value']
                print(f"   • {name}: {field} = '{value}' ({status})")
            else:
                print(f"   • {name}: {status}")

if __name__ == "__main__":
    main()
