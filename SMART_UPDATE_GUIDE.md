# 智能更新功能使用指南

## 🎯 功能概述

智能更新功能为人员档案更新提供了更灵活、更简洁的方式，支持：
- **智能字段映射**：使用简单字段名自动映射到正确位置
- **嵌套路径支持**：支持明确指定嵌套路径
- **混合更新模式**：可以同时使用多种更新方式
- **向后兼容**：完全兼容现有更新方式

## 🚀 新增API端点

### 1. 智能更新接口
```
PUT /humanrelation/person/{person_id}/smart_update
```

### 2. 快速更新接口
```
PUT /humanrelation/person/{person_id}/quick_update
```

## 📝 使用方式

### 方式1：简单字段映射（推荐）

最简单的使用方式，系统自动找到字段位置：

```javascript
// 前端代码
const updateData = {
  user_id: "zhangzheng51",
  key_attributes: {
    "微信": "new_wechat",
    "phone": "13900139000",
    "company": "新公司",
    "position": "产品经理"
  }
};

fetch('/humanrelation/person/123/smart_update', {
  method: 'PUT',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify(updateData)
});
```

### 方式2：明确路径（精确控制）

当需要精确控制更新位置时：

```javascript
const updateData = {
  user_id: "zhangzheng51",
  key_attributes: {
    "基本信息.联系方式.微信": "explicit_wechat",
    "基本信息.职业信息.职位": "高级产品经理"
  }
};
```

### 方式3：混合模式（灵活使用）

可以同时使用简单映射和明确路径：

```javascript
const updateData = {
  user_id: "zhangzheng51",
  key_attributes: {
    "phone": "13900139000",  // 简单映射
    "基本信息.职业信息.工作地点": "北京朝阳区"  // 明确路径
  }
};
```

### 方式4：快速更新（移动端友好）

专为移动端设计的简洁接口：

```javascript
const updateData = {
  user_id: "zhangzheng51",
  phone: "13900139000",
  wechat: "mobile_wechat",
  email: "<EMAIL>",
  company: "新公司",
  position: "新职位"
};

fetch('/humanrelation/person/123/quick_update', {
  method: 'PUT',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify(updateData)
});
```

## 🗺️ 支持的字段映射

### 联系方式
| 简单字段 | 映射路径 |
|---------|----------|
| `微信` / `wechat` | `基本信息.联系方式.社交账号.微信` |
| `电话` / `phone` | `基本信息.联系方式.电话` |
| `邮箱` / `email` | `基本信息.联系方式.邮箱` |

### 职业信息
| 简单字段 | 映射路径 |
|---------|----------|
| `公司` / `company` | `基本信息.职业信息.公司` |
| `职位` / `position` | `基本信息.职业信息.职位` |
| `工作地点` / `workplace` | `基本信息.职业信息.工作地点` |

### 基本信息
| 简单字段 | 映射路径 |
|---------|----------|
| `性别` / `gender` | `基本信息.性别` |
| `年龄` / `age` | `基本信息.年龄` |
| `家乡` / `hometown` | `基本信息.家乡` |
| `当前城市` / `city` | `基本信息.当前城市` |

### 冲突字段处理
| 字段 | 优先级1 | 优先级2 |
|------|---------|---------|
| `地址` / `address` | `基本信息.联系方式.地址` | `基本信息.职业信息.工作地点` |

## ⚠️ 注意事项

### 1. 字段冲突
当字段名有歧义时（如"地址"），系统会：
- 使用优先级最高的路径
- 在日志中记录警告信息
- 建议使用明确路径避免歧义

### 2. 向后兼容
- 原有的更新接口 `/humanrelation/change_person/{person_id}` 继续正常工作
- 现有前端代码无需修改
- 可以逐步迁移到新接口

### 3. 错误处理
- 智能更新失败时自动降级到原有功能
- 提供详细的错误信息和处理建议
- 支持功能开关控制

## 🔧 配置选项

可以通过Lion配置中心控制功能：

```json
{
  "humanrelation.enable_smart_update": "true",
  "humanrelation.smart_update_test_users": "user1,user2,user3"
}
```

## 📊 使用建议

### 移动端
推荐使用快速更新接口，代码最简洁：
```javascript
// 移动端推荐
updatePerson({
  phone: "13900139000",
  wechat: "new_wechat"
});
```

### Web端
推荐使用智能更新接口，支持更复杂的场景：
```javascript
// Web端推荐
updatePerson({
  key_attributes: {
    "phone": "13900139000",
    "基本信息.职业信息.工作地点": "具体地址"
  }
});
```

### 后台管理
可以使用明确路径，确保精确控制：
```javascript
// 后台管理推荐
updatePerson({
  key_attributes: {
    "基本信息.联系方式.电话": "13900139000",
    "基本信息.职业信息.公司": "新公司"
  }
});
```

## 🧪 测试

运行测试脚本验证功能：
```bash
python test_smart_update.py
```

## 📈 性能优化

- 智能处理只在需要时触发
- 自动降级机制确保稳定性
- 保持与现有AI格式化功能的兼容性
