# 批量头像分配功能

## 功能说明

为解决历史数据中人员没有头像的问题，新增了批量头像分配功能。该功能可以：

1. **智能识别角色**：根据姓名、关系、属性自动识别人员角色
2. **批量处理**：一次性处理多个人员的头像分配
3. **安全预览**：支持试运行模式，先预览再执行
4. **灵活配置**：支持指定用户或全局处理

## 快速使用

### 1. 试运行（推荐先使用）

```bash
curl -X POST "http://localhost:8000/humanrelation/batch_assign_avatars" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "your_user_id",
    "limit": 50,
    "dry_run": true
  }'
```

### 2. 实际执行

确认预览结果无误后：

```bash
curl -X POST "http://localhost:8000/humanrelation/batch_assign_avatars" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "your_user_id", 
    "limit": 50,
    "dry_run": false
  }'
```

### 3. 使用测试脚本

```bash
# Python测试脚本
python test_batch_avatar.py your_user_id

# Bash测试脚本  
./curl_examples.sh your_user_id
```

## 文件说明

| 文件 | 说明 |
|------|------|
| `app/service/batch_avatar_service.py` | 核心服务逻辑 |
| `app/service/avatar_service.py` | 智能头像分配 |
| `docs/batch_avatar_assignment_guide.md` | 详细使用指南 |
| `test_batch_avatar.py` | Python测试脚本 |
| `curl_examples.sh` | Bash测试脚本 |

## 接口参数

- `user_id`: 用户ID（空字符串表示处理所有用户）
- `limit`: 每次处理数量（默认100）
- `dry_run`: 试运行模式（默认true）

## 头像分配逻辑

1. **姓名识别**：妈妈、爸爸、儿子、女儿等
2. **关系识别**：从人际关系中提取角色信息
3. **属性识别**：从关键属性中识别角色
4. **性别细化**：结合性别信息细化角色
5. **默认兜底**：无法识别时使用默认头像

## 注意事项

1. **先试运行**：建议先使用 `dry_run: true` 预览结果
2. **分批处理**：大量数据建议分批处理
3. **头像配置**：确保Lion配置中心已配置头像资源池
4. **权限控制**：该功能会修改数据库，请谨慎使用

## 示例响应

```json
{
  "result": "success",
  "message": "批量头像分配完成 (试运行)",
  "processed_count": 25,
  "updated_count": 0,
  "failed_count": 0,
  "dry_run": true,
  "details": [
    {
      "person_id": "abc-123",
      "canonical_name": "妈妈",
      "old_avatar": "",
      "new_avatar": "https://example.com/avatar/mom1.jpg",
      "status": "preview"
    }
  ]
}
```

## 相关接口

- `GET /humanrelation/avatar_pools` - 查看头像资源池配置
- `PUT /humanrelation/change_person/{person_id}` - 手动修改单个人员头像
