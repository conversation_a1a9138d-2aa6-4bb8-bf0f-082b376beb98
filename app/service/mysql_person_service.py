########################################################
# MySQL人员服务 - 替代ES人员服务
########################################################

import json
from uuid import uuid4

from my_mysql.entity.person_table import delete_person, get_person_by_id, insert_person, person_memory, update_person
from my_mysql.sql_client import CLIENT
from sqlalchemy import select
from utils.logger import logger

# 智能字段映射配置
SMART_FIELD_MAPPINGS = {
    # 联系方式相关
    "微信": ["基本信息.联系方式.社交账号.微信"],
    "wechat": ["基本信息.联系方式.社交账号.微信"],
    "电话": ["基本信息.联系方式.电话"],
    "phone": ["基本信息.联系方式.电话"],
    "邮箱": ["基本信息.联系方式.邮箱"],
    "email": ["基本信息.联系方式.邮箱"],
    # 职业相关
    "公司": ["基本信息.职业信息.公司"],
    "company": ["基本信息.职业信息.公司"],
    "职位": ["基本信息.职业信息.职位"],
    "position": ["基本信息.职业信息.职位"],
    "工作地点": ["基本信息.职业信息.工作地点"],
    "workplace": ["基本信息.职业信息.工作地点"],
    # 基本信息
    "性别": ["基本信息.性别"],
    "gender": ["基本信息.性别"],
    "年龄": ["基本信息.年龄"],
    "age": ["基本信息.年龄"],
    "家乡": ["基本信息.家乡"],
    "hometown": ["基本信息.家乡"],
    "当前城市": ["基本信息.当前城市"],
    "city": ["基本信息.当前城市"],
    # 有冲突的字段 - 返回多个可能路径，按优先级排序
    "地址": [
        "基本信息.联系方式.地址",  # 优先级1：联系地址
        "基本信息.职业信息.工作地点",  # 优先级2：工作地址
    ],
    "address": [
        "基本信息.联系方式.地址",
        "基本信息.职业信息.工作地点",
    ],
}


def _smart_field_mapping(field_name: str) -> list:
    """
    智能字段映射，处理常见别名和冲突

    Args:
        field_name: 字段名

    Returns:
        list: 可能的路径列表，按优先级排序
    """
    return SMART_FIELD_MAPPINGS.get(field_name, [])


def _parse_nested_path(path_str: str) -> list:
    """
    解析嵌套路径字符串为路径列表

    Args:
        path_str: 路径字符串，如 "基本信息.联系方式.微信"

    Returns:
        list: 路径列表，如 ["基本信息", "联系方式", "微信"]
    """
    return path_str.split(".")


def _set_nested_value_by_path(nested_dict: dict, path_str: str, value) -> dict:
    """
    根据路径字符串设置嵌套字典中的值

    Args:
        nested_dict: 嵌套字典
        path_str: 路径字符串，如 "基本信息.联系方式.微信"
        value: 要设置的值

    Returns:
        dict: 更新后的字典
    """
    path = _parse_nested_path(path_str)
    current = nested_dict

    # 创建嵌套结构
    for i, key in enumerate(path[:-1]):
        if key not in current:
            current[key] = {}
        elif not isinstance(current[key], dict):
            current[key] = {}
        current = current[key]

    # 设置最终值
    current[path[-1]] = value
    return nested_dict


def _get_nested_value_by_path(nested_dict: dict, path_str: str):
    """
    根据路径字符串获取嵌套字典中的值

    Args:
        nested_dict: 嵌套字典
        path_str: 路径字符串

    Returns:
        any: 对应的值，如果路径不存在返回None
    """
    path = _parse_nested_path(path_str)
    current = nested_dict

    for key in path:
        if not isinstance(current, dict) or key not in current:
            return None
        current = current[key]

    return current


def _needs_smart_processing(kwargs: dict) -> bool:
    """
    判断是否需要智能处理

    Args:
        kwargs: 更新参数

    Returns:
        bool: 是否需要智能处理
    """
    key_attributes = kwargs.get("key_attributes", {})
    if not key_attributes:
        return False

    # 检查是否包含需要智能处理的字段
    for field in key_attributes.keys():
        if field in SMART_FIELD_MAPPINGS or "." in field:
            return True

    return False


def _format_to_array(value, field_name: str) -> list:
    """
    将字段值格式化为数组格式

    Args:
        value: 字段值（可能是字符串、列表等）
        field_name: 字段名称

    Returns:
        list: 格式化后的数组
    """
    if not value:
        return []

    # 如果已经是列表，直接返回
    if isinstance(value, list):
        return [str(item).strip() for item in value if str(item).strip()]

    # 转换为字符串处理
    value_str = str(value).strip()
    if not value_str:
        return []

    # 检查是否是旧格式的管道符分隔数据
    if "|" in value_str:
        # 兼容旧数据：按管道符分割
        parts = [part.strip() for part in value_str.split("|") if part.strip()]
        logger.info(f"[数组格式化] 兼容旧数据 {field_name}: '{value_str}' -> {parts}")
        return parts

    # 简单启发式判断：是否为列表数据
    list_indicators = [
        # 明显的分隔符
        "，",
        "；",
        "、",
        ",",
        ";",
        # 空格分隔的短词（可能是标签）
    ]

    # 描述性文本的特征
    descriptive_indicators = [
        "很",
        "了",
        "的",
        "是",
        "在",
        "我",
        "他",
        "她",
        "我们",
        "他们",
        "觉得",
        "感觉",
        "认为",
        "喜欢",
        "不喜欢",
        "但是",
        "不过",
        "然而",
        "。",
        "！",
        "？",
        "...",
        "…",
    ]

    # 检查是否包含明显的列表分隔符
    has_list_separators = any(sep in value_str for sep in list_indicators)

    # 检查是否包含描述性特征
    has_descriptive_features = any(indicator in value_str for indicator in descriptive_indicators)

    if has_list_separators and not has_descriptive_features:
        # 可能是列表数据，尝试分割
        for sep in ["，", "；", "、", ",", ";"]:
            if sep in value_str:
                parts = [part.strip() for part in value_str.split(sep) if part.strip()]
                if len(parts) > 1:
                    logger.info(f"[数组格式化] 列表数据 {field_name}: '{value_str}' -> {parts}")
                    return parts

        # 尝试空格分割（仅当没有描述性特征时）
        if " " in value_str and len(value_str.split()) <= 5:  # 限制分割数量
            parts = [part.strip() for part in value_str.split() if part.strip()]
            if len(parts) > 1 and all(len(part) <= 10 for part in parts):  # 每个部分都比较短
                logger.info(f"[数组格式化] 空格分割 {field_name}: '{value_str}' -> {parts}")
                return parts

    # 默认作为单个描述性文本
    logger.info(f"[数组格式化] 描述性文本 {field_name}: '{value_str}' -> ['{value_str}']")
    return [value_str]


def _should_format_field(field_name: str, value: str) -> bool:
    """
    判断字段是否需要格式化

    Args:
        field_name: 字段名称
        value: 字段值

    Returns:
        bool: 是否需要格式化
    """
    # 跳过基本信息等嵌套结构
    if field_name == "基本信息":
        return False

    # 跳过空值或非字符串
    if not isinstance(value, str) or not value.strip():
        return False

    # 跳过太短的值（可能不是多值字段）
    if len(value.strip()) < 3:
        return False

    # 跳过明显的单值字段
    single_value_fields = ["性别", "年龄", "职业", "关系", "当前城市", "家乡"]
    if field_name in single_value_fields:
        return False

    return True


def normalize_multi_value_fields(key_attributes: dict) -> dict:
    """
    智能统一多值字段的格式，将可能的多值字段转换为数组格式

    Args:
        key_attributes: 用户属性字典

    Returns:
        dict: 格式化后的属性字典（多值字段为数组格式）
    """
    if not isinstance(key_attributes, dict):
        return key_attributes

    result = {}

    # 递归处理嵌套字典
    def process_nested_dict(data, path=""):
        if isinstance(data, dict):
            processed = {}
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key

                if isinstance(value, dict):
                    # 递归处理嵌套字典
                    processed[key] = process_nested_dict(value, current_path)
                elif _should_format_field(key, value):
                    # 需要格式化的字段，转换为数组
                    formatted_array = _format_to_array(value, key)
                    processed[key] = formatted_array
                    logger.info(f"[数组格式化] {current_path}: {type(value).__name__} -> 数组[{len(formatted_array)}]")
                else:
                    # 不需要格式化的字段，保持原样
                    processed[key] = value
            return processed
        else:
            return data

    result = process_nested_dict(key_attributes)
    return result


def _normalize_field_for_display(value):
    """
    标准化字段值用于显示，确保多值字段为数组格式

    Args:
        value: 字段值

    Returns:
        标准化后的值
    """
    if isinstance(value, list):
        return value
    elif isinstance(value, str) and "|" in value:
        # 兼容旧的管道符格式
        return [part.strip() for part in value.split("|") if part.strip()]
    elif value:
        return [str(value)]
    else:
        return []


def _normalize_key_attributes_for_display(key_attributes: dict) -> dict:
    """
    标准化key_attributes用于显示，确保多值字段为数组格式

    Args:
        key_attributes: 原始属性字典

    Returns:
        dict: 标准化后的属性字典
    """
    if not isinstance(key_attributes, dict):
        return key_attributes

    result = {}

    def process_nested_dict(data):
        if isinstance(data, dict):
            processed = {}
            for key, value in data.items():
                if isinstance(value, dict):
                    processed[key] = process_nested_dict(value)
                elif _should_format_field(key, value):
                    # 多值字段标准化为数组
                    processed[key] = _normalize_field_for_display(value)
                else:
                    processed[key] = value
            return processed
        else:
            return data

    return process_nested_dict(key_attributes)


def add_person(
    user_id: str,
    canonical_name: str = "",
    aliases=None,  # 可以是字符串或列表
    relationships: list = [],
    profile_summary: str = "",
    key_attributes: dict = {},
    avatar: str = "",
    is_user: bool = False,
    person_id: str = "",
    intimacy_score: int = 0,
):  # 添加人员到MySQL
    if not person_id:
        person_id = str(uuid4())

    # 数据库插入流程日志
    logger.info(f"[数据库插入] add_person接收参数 - 人物: {canonical_name}")
    logger.info(f"[数据库插入] aliases: {aliases} (type: {type(aliases)})")
    logger.info(f"[数据库插入] relationships: {relationships} (type: {type(relationships)})")
    logger.info(f"[数据库插入] key_attributes: {type(key_attributes)}")

    # 在写入前格式化key_attributes
    if key_attributes:
        key_attributes = normalize_multi_value_fields(key_attributes)
        logger.info(f"[写入格式化] 人员 '{canonical_name}' 的属性已格式化")

    # 如果没有提供头像，自动分配头像
    if not avatar:
        from service.avatar_service import assign_avatar_for_new_person

        avatar = assign_avatar_for_new_person(
            canonical_name=canonical_name, relationships=relationships, key_attributes=key_attributes
        )
        logger.info(f"[头像分配] 为人员 '{canonical_name}' 自动分配头像: {avatar}")

    try:
        success = insert_person(
            user_id=user_id,
            person_id=person_id,
            is_user=is_user,
            canonical_name=canonical_name,
            aliases=aliases,
            relationships=relationships,
            profile_summary=profile_summary,
            key_attributes=key_attributes,
            avatar=avatar,
            intimacy_score=intimacy_score,
        )
        if success:
            logger.info(f"MySQL添加人员成功: {person_id} for user: {user_id}")

            # 同步到ES
            person_data = {
                "user_id": user_id,
                "canonical_name": canonical_name,
                "aliases": aliases,
                "relationships": relationships,
                "profile_summary": profile_summary,
                "key_attributes": key_attributes,
                "avatar": avatar,
                "is_user": is_user,
            }
            sync_person_to_es(person_id, person_data)

            return {"result": "success", "person_id": person_id}
        else:
            return {"result": "error", "reason": "插入失败"}
    except Exception as e:
        logger.error(f"MySQL添加人员失败: {str(e)}")
        return {"result": "error", "reason": str(e)}


def get_all_persons_mysql(
    user_id: str, limit: int = 100, offset: int = 0, order_by: str = "updated_at DESC", include_total: bool = True
):  # 获取所有人员列表（优化版）
    """
    获取所有人员列表，支持排序和总数统计

    Args:
        user_id: 用户ID
        limit: 限制数量，默认100
        offset: 偏移量，默认0
        order_by: 排序规则，支持：
            - "updated_at DESC": 按更新时间倒序（默认，最近更新的在前）
            - "created_at DESC": 按创建时间倒序（最近创建的在前）
            - "canonical_name ASC": 按姓名正序（A-Z）
            - "intimacy_score DESC": 按亲密度倒序（亲密度高的在前）
            - "person_id ASC": 按ID正序
        include_total: 是否包含总数统计，默认True（优化为默认包含）
    """
    try:
        # 导入新的排序函数
        from my_mysql.entity.person_table import get_all_persons_ordered, get_persons_count

        # 使用支持排序的函数获取人员列表
        persons = get_all_persons_ordered(user_id=user_id, limit=limit, offset=offset, order_by=order_by)
        processed_persons = []
        default_attributes = {"关系": "", "年龄": "", "职业": ""}

        for person in persons:
            # 确保 key_attributes 不是 None
            raw_attributes = person.get("key_attributes")
            if raw_attributes:
                # 确保是字典而非JSON字符串
                key_attributes = json.loads(raw_attributes) if isinstance(raw_attributes, str) else raw_attributes
            else:
                key_attributes = {}

            # 补全必需字段
            key_attributes = {**default_attributes, **key_attributes}

            person["key_attributes"] = key_attributes

            # 确保 relationships 是列表或null
            raw_relationships = person.get("relationships")
            if raw_relationships:
                person["relationships"] = (
                    json.loads(raw_relationships) if isinstance(raw_relationships, str) else raw_relationships
                )
            else:
                person["relationships"] = None  # 遵从接口定义，返回null

            processed_persons.append(person)

        # 构建返回结果
        result = {
            "result": "success",
            "persons": processed_persons,
            "pagination": {"limit": limit, "offset": offset, "count": len(processed_persons)},
        }

        # 如果需要总数统计（默认包含）
        if include_total:
            total_count = get_persons_count(user_id=user_id)
            result["pagination"]["total"] = total_count
            result["pagination"]["has_more"] = (offset + limit) < total_count
            result["pagination"]["total_pages"] = (total_count + limit - 1) // limit if limit > 0 else 0
            result["pagination"]["current_page"] = (offset // limit) + 1 if limit > 0 else 1

            # 添加分页建议
            if total_count > 200:
                result["pagination"]["suggestions"] = {
                    "large_dataset": True,
                    "recommended_page_size": 100,
                    "estimated_pages": (total_count + 99) // 100,
                    "message": f"检测到大量数据({total_count}条)，建议使用分页浏览",
                }

        logger.info(f"MySQL获取人员列表成功，共{len(processed_persons)}人 for user: {user_id}, 排序: {order_by}")
        return result
    except Exception as e:
        logger.error(f"MySQL获取人员列表失败: {str(e)}")
        return {"result": "error", "reason": str(e), "persons": []}


def get_person_by_id_mysql(user_id: str, person_id: str):  # 根据ID获取人员详情
    try:
        person = get_person_by_id(user_id=user_id, person_id=person_id)
        if person:
            # 处理JSON字段
            if person.get("relationships"):
                person["relationships"] = (
                    json.loads(person["relationships"])
                    if isinstance(person["relationships"], str)
                    else person["relationships"]
                )
            if person.get("key_attributes"):
                key_attributes = (
                    json.loads(person["key_attributes"])
                    if isinstance(person["key_attributes"], str)
                    else person["key_attributes"]
                )
                person["key_attributes"] = key_attributes

            logger.info(f"MySQL获取人员详情成功: {person_id} for user: {user_id}")
            return {"result": "success", "person": person}
        else:
            return {"result": "error", "reason": "人员不存在", "person": None}
    except Exception as e:
        logger.error(f"MySQL获取人员详情失败: {str(e)}")
        return {"result": "error", "reason": str(e), "person": None}


def _smart_process_fields(kwargs: dict) -> dict:
    """
    智能处理字段，支持嵌套路径查找和字段映射
    将路径字段转换为嵌套结构，而不是平级字段

    Args:
        kwargs: 原始更新参数

    Returns:
        dict: 处理后的更新参数
    """
    key_attributes = kwargs.get("key_attributes", {})
    if not key_attributes:
        return kwargs

    processed_attributes = {}
    warnings = []

    for field, value in key_attributes.items():
        # 模式1：明确路径（如 "基本信息.联系方式.微信"）
        if "." in field:
            # 将路径转换为嵌套结构
            _set_nested_value_by_path(processed_attributes, field, value)
            logger.info(f"[智能更新] 明确路径: {field} = {value}")

        # 模式2：智能映射（如 "微信", "phone"）
        elif field in SMART_FIELD_MAPPINGS:
            paths = _smart_field_mapping(field)
            if len(paths) == 1:
                # 将映射路径转换为嵌套结构
                _set_nested_value_by_path(processed_attributes, paths[0], value)
                logger.info(f"[智能更新] 智能映射: {field} -> {paths[0]} = {value}")
            else:
                # 有冲突，使用第一个路径，但记录警告
                _set_nested_value_by_path(processed_attributes, paths[0], value)
                warning_msg = f"字段 '{field}' 有多个可能位置，使用 {paths[0]}"
                warnings.append(warning_msg)
                logger.warning(f"[智能更新] {warning_msg}")

        # 模式3：保持原样（平级字段或未知字段）
        else:
            processed_attributes[field] = value
            logger.info(f"[智能更新] 保持原样: {field} = {value}")

    # 构建处理后的参数
    result_kwargs = kwargs.copy()
    result_kwargs["key_attributes"] = processed_attributes

    # 如果有警告，可以在日志中记录
    if warnings:
        logger.info(f"[智能更新] 处理警告: {warnings}")

    return result_kwargs


def update_person_mysql(user_id: str, person_id: str, **kwargs):  # 更新人员信息
    try:
        success = update_person(user_id=user_id, person_id=person_id, **kwargs)
        if success:
            logger.info(f"MySQL更新人员成功: {person_id} for user: {user_id}")

            # 获取更新后的人员信息并同步到ES
            try:
                person_result = get_person_by_id_mysql(user_id, person_id)
                if person_result.get("result") == "success" and person_result.get("person"):
                    sync_person_to_es(person_id, person_result["person"])
            except Exception as sync_e:
                logger.error(f"更新后同步到ES失败: {sync_e}")

            return {"result": "success"}
        else:
            return {"result": "error", "reason": "更新失败或人员不存在"}
    except Exception as e:
        logger.error(f"MySQL更新人员失败: {str(e)}")
        return {"result": "error", "reason": str(e)}


def update_person_smart(user_id: str, person_id: str, **kwargs):
    """
    智能更新人员信息，支持嵌套路径查找和字段映射

    Args:
        user_id: 用户ID
        person_id: 人员ID
        **kwargs: 更新参数

    Returns:
        dict: 更新结果
    """
    try:
        # 检查是否需要智能处理
        if not _needs_smart_processing(kwargs):
            logger.info(f"[智能更新] 无需智能处理，使用原有逻辑")
            return update_person_mysql(user_id, person_id, **kwargs)

        # 智能处理字段
        processed_kwargs = _smart_process_fields(kwargs)
        logger.info(f"[智能更新] 字段处理完成，开始更新人员: {person_id}")

        # 调用原有更新逻辑
        return update_person_mysql(user_id, person_id, **processed_kwargs)

    except Exception as e:
        logger.error(f"[智能更新] 智能更新失败，降级到原有功能: {e}")
        # 自动降级到原有功能
        return update_person_mysql(user_id, person_id, **kwargs)


def search_persons_by_name_mysql(
    user_id: str, name: str, limit: int = 10
):  # 按姓名搜索人员（优化版，支持部分字符匹配）
    try:
        from my_mysql import sql_client
        from my_mysql.entity.person_table import person_memory
        from sqlalchemy import or_, select

        # 去除搜索词的空格
        name = name.strip()
        if not name:
            return {"result": "success", "persons": []}

        all_results = []
        person_ids_seen = set()  # 用于去重

        # 1. 先进行精确匹配（优先级最高）
        exact_stmt = (
            select(person_memory)
            .where(person_memory.c.user_id == user_id, person_memory.c.canonical_name == name)
            .limit(limit)
        )
        exact_results = sql_client.select_many(exact_stmt)

        # 添加精确匹配结果
        for row in exact_results:
            person = dict(row._mapping)
            person_id = person.get("person_id")
            if person_id not in person_ids_seen:
                all_results.append(row)
                person_ids_seen.add(person_id)

        # 2. 如果还有容量，进行模糊匹配（支持单字符搜索）
        remaining_limit = limit - len(all_results)
        if remaining_limit > 0:
            fuzzy_stmt = (
                select(person_memory)
                .where(
                    person_memory.c.user_id == user_id,
                    or_(person_memory.c.canonical_name.like(f"%{name}%"), person_memory.c.aliases.like(f"%{name}%")),
                )
                .limit(remaining_limit * 2)  # 查询更多结果用于去重
            )
            fuzzy_results = sql_client.select_many(fuzzy_stmt)

            # 添加模糊匹配结果（去重）
            for row in fuzzy_results:
                if len(all_results) >= limit:
                    break
                person = dict(row._mapping)
                person_id = person.get("person_id")
                if person_id not in person_ids_seen:
                    all_results.append(row)
                    person_ids_seen.add(person_id)

        # 处理结果
        processed_persons = []
        for row in all_results:
            person = dict(row._mapping)
            try:
                if person.get("relationships"):
                    person["relationships"] = json.loads(person["relationships"])
                if person.get("key_attributes"):
                    key_attributes = json.loads(person["key_attributes"])
                    person["key_attributes"] = key_attributes
            except (json.JSONDecodeError, TypeError):
                person["relationships"] = []
                person["key_attributes"] = {}
            processed_persons.append(person)

        # 预览信息（只显示前3个人的姓名）
        preview = [p.get("canonical_name", "未知") for p in processed_persons[:3]]
        if len(processed_persons) > 3:
            preview.append(f"...等{len(processed_persons)}人")

        logger.info(
            f"MySQL搜索人员成功，关键词:'{name}'，结果数:{len(processed_persons)} for user: {user_id}，预览:{preview}"
        )
        return {"result": "success", "persons": processed_persons}
    except Exception as e:
        logger.error(f"MySQL搜索人员失败: {str(e)}")
        return {"result": "error", "reason": str(e), "persons": []}


def delete_person_mysql(user_id: str, person_id: str):  # 删除人员
    try:
        success = delete_person(user_id=user_id, person_id=person_id)
        if success:
            logger.info(f"MySQL删除人员成功: {person_id} for user: {user_id}")

            # 从ES中删除对应的人物档案
            delete_person_from_es(person_id)

            return {"result": "success"}
        else:
            return {"result": "error", "reason": "删除失败或人员不存在"}
    except Exception as e:
        logger.error(f"MySQL删除人员失败: {str(e)}")
        return {"result": "error", "reason": str(e)}


def search_persons_by_alias(user_id: str, alias: str, limit: int = 10):  # 按别名搜索人员
    try:
        from my_mysql import sql_client
        from my_mysql.entity.person_table import person_memory
        from sqlalchemy import select

        stmt = (
            select(person_memory)
            .where(person_memory.c.user_id == user_id, person_memory.c.aliases.like(f"%{alias}%"))
            .limit(limit)
        )
        results = sql_client.select_many(stmt)
        persons = []
        for row in results:
            person = dict(row._mapping)
            if person["relationships"]:
                person["relationships"] = json.loads(person["relationships"])
            if person["key_attributes"]:
                person["key_attributes"] = json.loads(person["key_attributes"])
            persons.append(person)
        logger.info(f"MySQL按别名搜索人员成功，关键词:{alias}，结果数:{len(persons)} for user: {user_id}")
        return {"result": "success", "persons": persons}
    except Exception as e:
        logger.error(f"MySQL按别名搜索人员失败: {str(e)}")
        return {"result": "error", "reason": str(e), "persons": []}


def search_persons_by_attributes_mysql(user_id: str, search_attributes: list, limit: int = 10):
    """按属性检索人物，支持反向属性查询"""
    try:
        from sqlalchemy import and_, or_, text

        conn = CLIENT.connect()
        try:
            # 构建查询条件
            conditions = [person_memory.c.user_id == user_id]

            for attr in search_attributes:
                if not attr:
                    continue

                # 在profile_summary中搜索
                profile_condition = person_memory.c.profile_summary.like(f"%{attr}%")

                # 在key_attributes JSON字段中搜索
                # 使用JSON_SEARCH函数查找JSON中的值
                json_condition = text(f"JSON_SEARCH(key_attributes, 'one', '%{attr}%') IS NOT NULL")

                # 组合条件
                conditions.append(or_(profile_condition, json_condition))

            # 执行查询
            stmt = select(person_memory).where(and_(*conditions)).limit(limit)
            results = conn.execute(stmt).fetchall()

            # 处理结果
            processed_persons = []
            for result in results:
                person = dict(result._mapping)
                # 处理JSON字段
                if person.get("key_attributes"):
                    try:
                        person["key_attributes"] = json.loads(person["key_attributes"])
                    except (json.JSONDecodeError, TypeError):
                        person["key_attributes"] = {}
                if person.get("relationships"):
                    try:
                        person["relationships"] = json.loads(person["relationships"])
                    except (json.JSONDecodeError, TypeError):
                        person["relationships"] = []
                processed_persons.append(person)

            logger.info(f"MySQL属性检索成功，找到{len(processed_persons)}个匹配的人物")
            return {"result": "success", "persons": processed_persons}

        finally:
            conn.close()
    except Exception as e:
        logger.error(f"MySQL属性检索失败: {str(e)}")
        return {"result": "error", "reason": str(e), "persons": []}


def get_user_person(user_id: str):
    """获取用户本人的档案"""
    try:
        conn = CLIENT.connect()
        try:
            stmt = select(person_memory).where(
                (person_memory.c.user_id == user_id) & (person_memory.c.is_user.is_(True))
            )
            result = conn.execute(stmt).fetchone()
            if result:
                person = dict(result._mapping)
                # 处理JSON字段
                if person.get("relationships"):
                    person["relationships"] = json.loads(person["relationships"])
                if person.get("key_attributes"):
                    key_attributes = json.loads(person["key_attributes"])
                    person["key_attributes"] = key_attributes
                return person
            return None
        finally:
            conn.close()
    except Exception as e:
        logger.error(f"获取用户本人档案失败: {e}")
        return None


def ensure_user_profile_exists(user_id: str, user_name: str = None):
    """确保用户本人的档案存在，如果不存在则创建"""
    try:
        # 检查是否已存在用户本人档案
        existing_user = get_user_person(user_id)
        if existing_user:
            logger.info(f"用户 {user_id} 的本人档案已存在: {existing_user['person_id']}")
            return {"result": "success", "person_id": existing_user["person_id"], "action": "exists"}

        # 创建用户本人档案
        person_id = f"user_{user_id}_{str(uuid4())[:8]}"  # 特殊的person_id格式
        canonical_name = user_name or f"用户_{user_id}"  # 默认名称

        # 用户本人的特殊属性
        key_attributes = {"身份": "系统用户", "创建方式": "系统自动创建"}

        profile_summary = "这是您的个人档案，系统会在这里记录与您相关的信息。"

        # 为用户本人分配默认头像
        from service.avatar_service import assign_avatar_for_new_person

        avatar_url = assign_avatar_for_new_person(
            canonical_name=canonical_name, relationships=[], key_attributes=key_attributes
        )

        success = insert_person(
            user_id=user_id,
            person_id=person_id,
            is_user=True,  # 关键：标记为用户本人
            canonical_name=canonical_name,
            aliases="我",  # 添加"我"作为别名
            relationships=[],
            profile_summary=profile_summary,
            key_attributes=key_attributes,
            avatar=avatar_url,
        )

        if success:
            logger.info(f"为用户 {user_id} 创建本人档案成功: {person_id}, 头像: {avatar_url}")
            return {"result": "success", "person_id": person_id, "action": "created"}
        else:
            logger.error(f"为用户 {user_id} 创建本人档案失败")
            return {"result": "error", "reason": "创建档案失败"}

    except Exception as e:
        logger.error(f"确保用户本人档案存在失败: {e}")
        return {"result": "error", "reason": str(e)}


def sync_person_to_es(person_id: str, person_data: dict):
    """将人物档案同步到ES，作为可检索的人物档案记忆"""
    try:
        from datetime import datetime

        from configs.lion_config import get_value
        from service.ESmemory.es_memory_client import client

        # 获取ES索引名称
        index_name = get_value("humanrelation.event_index_name", "memory_event_store")

        # 处理key_attributes，确保ES能正确解析
        key_attributes = person_data.get("key_attributes", {})
        if isinstance(key_attributes, dict):
            # 将字典转换为JSON字符串，以便ES的text字段能正确存储
            key_attributes_str = json.dumps(key_attributes, ensure_ascii=False)
        else:
            key_attributes_str = str(key_attributes)

        # 处理relationships，转换为JSON字符串以便ES存储
        relationships = person_data.get("relationships", [])
        logger.info(f"🔍 原始relationships数据: {relationships} (类型: {type(relationships)})")

        # 统一处理各种可能的relationships格式
        if isinstance(relationships, str):
            logger.info(f"🔍 处理字符串格式的relationships: {relationships}")
            # 如果是字符串，尝试解析为JSON
            try:
                relationships = json.loads(relationships)
                logger.info(f"✅ JSON解析成功: {relationships}")
            except json.JSONDecodeError:
                logger.info(f"❌ JSON解析失败，检查是否为Python对象字符串格式")
                # 检查是否是Python对象的字符串表示（如 {type=同事, target=USER}）
                if relationships.startswith("{") and relationships.endswith("}") and "=" in relationships:
                    logger.info(f"🔧 检测到Python对象字符串格式，尝试解析")
                    try:
                        # 尝试将Python对象字符串转换为标准格式
                        # {type=同事, target=USER} -> {"type": "同事", "target": "USER"}

                        # 移除外层大括号
                        content = relationships.strip("{}")
                        # 分割键值对
                        pairs = content.split(", ")
                        parsed_dict = {}
                        for pair in pairs:
                            if "=" in pair:
                                key, value = pair.split("=", 1)
                                parsed_dict[key.strip()] = value.strip()
                        relationships = [parsed_dict]
                        logger.info(f"✅ Python对象字符串解析成功: {relationships}")
                    except Exception as e:
                        logger.error(f"❌ Python对象字符串解析失败: {e}")
                        # 如果解析失败，将字符串作为单个关系处理
                        relationships = [{"type": "未知关系", "target": relationships}]
                else:
                    # 如果解析失败，将字符串作为单个关系处理
                    relationships = [{"type": "未知关系", "target": relationships}]
        elif isinstance(relationships, dict):
            # 如果是单个字典，转换为列表
            relationships = [relationships]
        elif relationships is None:
            relationships = []
        elif not isinstance(relationships, list):
            # 如果是其他类型，转换为字符串并包装为关系对象
            relationships = [{"type": "未知关系", "target": str(relationships)}]

        # 确保列表中的每个元素都是可序列化的
        processed_relationships = []
        for rel in relationships:
            if isinstance(rel, dict):
                # 确保字典中的值都是可序列化的
                processed_rel = {}
                for k, v in rel.items():
                    try:
                        json.dumps(v, ensure_ascii=False)
                        processed_rel[k] = v
                    except (TypeError, ValueError):
                        processed_rel[k] = str(v)
                processed_relationships.append(processed_rel)
            else:
                # 如果不是字典，转换为标准格式
                processed_relationships.append({"type": "未知关系", "target": str(rel)})

        relationships = processed_relationships

        # 最终调试信息
        relationships_json = json.dumps(relationships, ensure_ascii=False)
        logger.info(f"🎯 最终relationships数据: {relationships_json}")

        # 将relationships转换为JSON字符串，与key_attributes保持一致
        if isinstance(relationships, list):
            relationships_str = json.dumps(relationships, ensure_ascii=False)
        else:
            relationships_str = str(relationships)

        # 构建ES文档
        es_document = {
            "memory_type": "person_profile",  # 新的记忆类型
            "user_id": person_data.get("user_id"),
            "person_id": person_id,
            "memory_content": f"{person_data.get('canonical_name', '')} {person_data.get('profile_summary', '')}",
            "canonical_name": person_data.get("canonical_name", ""),
            "profile_summary": person_data.get("profile_summary", ""),
            "key_attributes": key_attributes_str,  # 存储为JSON字符串
            "aliases": person_data.get("aliases", ""),
            "relationships": relationships_str,  # 存储为JSON字符串
            "time_stamp": datetime.now().strftime("%Y-%m-%d-%H-%M"),
            "timestamp": datetime.now(),
            "is_user": person_data.get("is_user", False),
        }

        # 同步到ES
        doc_id = f"person_profile_{person_id}"
        client.index(index=index_name, id=doc_id, body=es_document)
        logger.info(f"人物档案同步到ES成功: {person_id}")

    except Exception as e:
        logger.error(f"人物档案同步到ES失败: {e}")


def delete_person_from_es(person_id: str):
    """从ES中删除人物档案"""
    try:
        from configs.lion_config import get_value
        from service.ESmemory.es_memory_client import client

        index_name = get_value("humanrelation.event_index_name", "memory_event_store")
        doc_id = f"person_profile_{person_id}"

        client.delete(index=index_name, id=doc_id, ignore=[404])
        logger.info(f"从ES删除人物档案成功: {person_id}")

    except Exception as e:
        logger.error(f"从ES删除人物档案失败: {e}")


def is_current_user(user_id: str, person_id: str) -> bool:
    """判断指定的 person_id 是否为当前用户本人"""
    try:
        person = get_person_by_id_mysql(user_id=user_id, person_id=person_id)
        return person and person.get("is_user", False)
    except Exception as e:
        logger.error(f"判断是否为当前用户失败: {e}")
        return False


# 使用示例：
#
# 1. 基本用法（按更新时间倒序，默认100条）
# result = get_all_persons_mysql(user_id="123")
#
# 2. 自定义分页和排序
# result = get_all_persons_mysql(
#     user_id="123",
#     limit=50,
#     offset=50,
#     order_by="canonical_name ASC"
# )
#
# 3. 包含总数统计
# result = get_all_persons_mysql(
#     user_id="123",
#     limit=100,
#     offset=0,
#     order_by="intimacy_score DESC",
#     include_total=True
# )
#
# 4. 大数据量分页示例（1000个关系）
# def get_all_persons_with_pagination(user_id: str, page: int = 1, page_size: int = 100):
#     """
#     获取所有人员的分页数据
#
#     Args:
#         user_id: 用户ID
#         page: 页码，从1开始
#         page_size: 每页数量，默认100
#
#     Returns:
#         dict: 包含人员列表和分页信息
#     """
#     offset = (page - 1) * page_size
#     return get_all_persons_mysql(
#         user_id=user_id,
#         limit=page_size,
#         offset=offset,
#         order_by="updated_at DESC",
#         include_total=True
#     )
#
# # 使用示例：
# # 获取第1页（前100个）
# page1 = get_all_persons_with_pagination(user_id="123", page=1)
# # 获取第2页（第101-200个）
# page2 = get_all_persons_with_pagination(user_id="123", page=2)
# # 获取第10页（第901-1000个）
# page10 = get_all_persons_with_pagination(user_id="123", page=10)
#
# 返回结果示例：
# {
#     "result": "success",
#     "persons": [...],
#     "pagination": {
#         "limit": 100,
#         "offset": 0,
#         "count": 100,
#         "total": 1000,
#         "has_more": true,
#         "total_pages": 10,
#         "current_page": 1,
#         "suggestions": {
#             "large_dataset": true,
#             "recommended_page_size": 100,
#             "estimated_pages": 10,
#             "message": "检测到大量数据(1000条)，建议使用分页浏览"
#         }
#     }
# }
