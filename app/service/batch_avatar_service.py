########################################################
# 批量头像分配服务
########################################################

import json
from typing import Dict, List, Optional

from service.avatar_service import assign_avatar_for_new_person
from service.mysql_person_service import get_all_persons_mysql, update_person_mysql
from utils.logger import logger


class BatchAvatarService:
    """批量头像分配服务"""

    def __init__(self):
        pass

    def _get_persons_need_avatar(self, user_id: Optional[str] = None, limit: int = 100) -> List[Dict]:
        """获取需要分配头像的人员列表"""
        try:
            if user_id:
                # 处理指定用户的数据
                persons_result = get_all_persons_mysql(user_id=user_id, limit=limit, offset=0)
                if persons_result.get("result") != "success":
                    logger.error(f"获取用户 {user_id} 的人员列表失败: {persons_result}")
                    return []
                
                all_persons = persons_result.get("persons", [])
            else:
                # 处理所有用户的数据（直接查询数据库）
                from my_mysql.entity.person_table import person_memory
                from my_mysql.sql_client import CLIENT
                from sqlalchemy import select, or_
                
                conn = CLIENT.connect()
                try:
                    # 查询没有头像或头像为空的人员
                    stmt = (
                        select(person_memory)
                        .where(
                            or_(
                                person_memory.c.avatar.is_(None),
                                person_memory.c.avatar == "",
                                person_memory.c.avatar == "https://via.placeholder.com/150x150/cccccc/666666?text=Avatar",
                            )
                        )
                        .limit(limit)
                    )
                    results = conn.execute(stmt).fetchall()
                    all_persons = [dict(row._mapping) for row in results]
                    
                    # 处理JSON字段
                    for person in all_persons:
                        if person.get("relationships"):
                            try:
                                person["relationships"] = json.loads(person["relationships"])
                            except (json.JSONDecodeError, TypeError):
                                person["relationships"] = []
                        if person.get("key_attributes"):
                            try:
                                person["key_attributes"] = json.loads(person["key_attributes"])
                            except (json.JSONDecodeError, TypeError):
                                person["key_attributes"] = {}
                finally:
                    conn.close()
            
            # 筛选需要分配头像的人员
            persons_need_avatar = []
            for person in all_persons:
                avatar = person.get("avatar", "")
                # 检查是否需要分配头像（没有头像或使用默认占位符头像）
                if not avatar or avatar == "https://via.placeholder.com/150x150/cccccc/666666?text=Avatar":
                    persons_need_avatar.append(person)
            
            logger.info(f"找到 {len(persons_need_avatar)} 个需要分配头像的人员")
            return persons_need_avatar
            
        except Exception as e:
            logger.error(f"获取需要分配头像的人员列表失败: {e}")
            return []

    def _assign_avatar_for_person(self, person: Dict) -> Dict:
        """为单个人员分配头像"""
        try:
            person_id = person.get("person_id")
            canonical_name = person.get("canonical_name", "")
            relationships = person.get("relationships", [])
            key_attributes = person.get("key_attributes", {})
            current_avatar = person.get("avatar", "")
            
            # 为人员分配头像
            new_avatar = assign_avatar_for_new_person(
                canonical_name=canonical_name,
                relationships=relationships,
                key_attributes=key_attributes
            )
            
            return {
                "person_id": person_id,
                "canonical_name": canonical_name,
                "old_avatar": current_avatar,
                "new_avatar": new_avatar,
                "user_id": person.get("user_id"),
                "status": "ready"
            }
            
        except Exception as e:
            logger.error(f"为人员 {person.get('canonical_name', 'Unknown')} 分配头像失败: {e}")
            return {
                "person_id": person.get("person_id"),
                "canonical_name": person.get("canonical_name", "Unknown"),
                "status": "error",
                "error": str(e)
            }

    def _update_person_avatar(self, assignment_result: Dict) -> Dict:
        """更新人员头像到数据库"""
        try:
            if assignment_result.get("status") != "ready":
                return assignment_result
            
            person_id = assignment_result["person_id"]
            user_id = assignment_result["user_id"]
            new_avatar = assignment_result["new_avatar"]
            canonical_name = assignment_result["canonical_name"]
            
            # 实际更新数据库
            update_result = update_person_mysql(
                user_id=user_id,
                person_id=person_id,
                avatar=new_avatar
            )
            
            if update_result.get("result") == "success":
                assignment_result["status"] = "success"
                logger.info(f"成功为 '{canonical_name}' 分配头像: {new_avatar}")
            else:
                assignment_result["status"] = "failed"
                assignment_result["error"] = update_result.get("reason", "未知错误")
                logger.error(f"为 '{canonical_name}' 分配头像失败: {update_result}")
            
            return assignment_result
            
        except Exception as e:
            logger.error(f"更新人员头像失败: {e}")
            assignment_result["status"] = "error"
            assignment_result["error"] = str(e)
            return assignment_result

    def batch_assign_avatars(
        self, 
        user_id: Optional[str] = None, 
        limit: int = 100, 
        dry_run: bool = True
    ) -> Dict:
        """
        批量为历史数据分配头像
        
        Args:
            user_id: 用户ID，如果为None则处理所有用户
            limit: 每次处理的最大数量
            dry_run: 是否为试运行模式
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始批量头像分配 - 用户: {user_id or '所有用户'}, 限制: {limit}, 试运行: {dry_run}")
            
            # 1. 获取需要分配头像的人员
            persons_need_avatar = self._get_persons_need_avatar(user_id, limit)
            
            if len(persons_need_avatar) == 0:
                return {
                    "result": "success",
                    "message": "没有需要分配头像的人员",
                    "processed_count": 0,
                    "updated_count": 0,
                    "failed_count": 0,
                    "dry_run": dry_run,
                    "details": []
                }
            
            # 2. 批量处理
            processed_count = 0
            updated_count = 0
            failed_count = 0
            results = []
            
            for person in persons_need_avatar:
                # 为人员分配头像
                assignment_result = self._assign_avatar_for_person(person)
                processed_count += 1
                
                if assignment_result.get("status") == "ready":
                    if dry_run:
                        # 试运行模式，只预览不实际更新
                        assignment_result["status"] = "preview"
                        logger.info(f"试运行 - 将为 '{assignment_result['canonical_name']}' 分配头像: {assignment_result['new_avatar']}")
                    else:
                        # 实际执行模式，更新数据库
                        assignment_result = self._update_person_avatar(assignment_result)
                        
                        if assignment_result.get("status") == "success":
                            updated_count += 1
                        else:
                            failed_count += 1
                else:
                    failed_count += 1
                
                results.append(assignment_result)
            
            # 3. 构建返回结果
            response = {
                "result": "success",
                "message": f"批量头像分配完成 ({'试运行' if dry_run else '实际执行'})",
                "processed_count": processed_count,
                "updated_count": updated_count,
                "failed_count": failed_count,
                "dry_run": dry_run,
                "details": results[:20]  # 只返回前20个详细结果，避免响应过大
            }
            
            if len(results) > 20:
                response["total_results"] = len(results)
                response["showing_first"] = 20
            
            logger.info(f"批量头像分配完成 - 处理: {processed_count}, 更新: {updated_count}, 失败: {failed_count}")
            return response
            
        except Exception as e:
            logger.error(f"批量头像分配失败: {e}")
            return {"result": "error", "reason": str(e)}


# 创建全局实例
batch_avatar_service = BatchAvatarService()


def batch_assign_avatars_for_user(user_id: str, limit: int = 100, dry_run: bool = True) -> Dict:
    """为指定用户批量分配头像的便捷函数"""
    return batch_avatar_service.batch_assign_avatars(user_id=user_id, limit=limit, dry_run=dry_run)


def batch_assign_avatars_for_all_users(limit: int = 100, dry_run: bool = True) -> Dict:
    """为所有用户批量分配头像的便捷函数"""
    return batch_avatar_service.batch_assign_avatars(user_id=None, limit=limit, dry_run=dry_run)
