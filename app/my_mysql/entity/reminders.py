########################################################
# 该文件用于操作 'reminders' 表。
# 严格按照提供的表结构和示例脚本模板创建。
########################################################

from datetime import datetime
from typing import List, Optional, Dict, Any

from my_mysql import sql_client
from sqlalchemy import Table, Column, MetaData, insert, update, select, \
     text, and_, or_, delete, Index
from sqlalchemy.dialects.mysql import BIGINT, VARCHAR, TEXT, DATETIME, ENUM, TIMESTAMP
from sqlalchemy import JSON as SQLAlchemyJSON
from utils.logger import logger

#
# CREATE TABLE `reminders` (
#   `reminder_id` bigint unsigned NOT NULL AUTO_INCREMENT,
#   `user_id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
#   `subject_person_id` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
#   `reminder_text_template` text COLLATE utf8mb4_unicode_ci,
#   `base_event_date` datetime NOT NULL,
#   `advance_notice_config` json DEFAULT NULL,
#   `recurrence_rule` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
#   `next_trigger_time` datetime NOT NULL,
#   `status` enum('active','processing','completed') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
#   `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
#   `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
#   PRIMARY KEY (`reminder_id`),
#   KEY `idx_user_id` (`user_id`),
#   KEY `idx_subject_person_id` (`subject_person_id`),
#   KEY `idx_status_and_trigger_time` (`status`,`next_trigger_time`)
# ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
#

# 定义 reminders 表的元数据
reminders_table = Table(
    'reminders', MetaData(),
    Column('reminder_id', BIGINT(unsigned=True), primary_key=True, autoincrement=True),
    Column('user_id', VARCHAR(128), nullable=False),
    Column('subject_person_id', VARCHAR(128), nullable=True),
    Column('reminder_text_template', TEXT, nullable=True),
    Column('base_event_date', DATETIME, nullable=False),
    Column('advance_notice_config', TEXT, nullable=True),  # 改为 TEXT 类型
    Column('recurrence_rule', VARCHAR(255), nullable=True),
    Column('next_trigger_time', DATETIME, nullable=False),
    Column('status', ENUM('active', 'processing', 'completed'), nullable=False, server_default='active'),
    Column('display_text', TEXT, nullable=True),  # 新增展示字段
    Column('created_at', TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP')),
    Column('updated_at', TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),

    # 定义索引
    Index('idx_user_id', 'user_id'),
    Index('idx_subject_person_id', 'subject_person_id'),
    Index('idx_status_and_trigger_time', 'status', 'next_trigger_time')
)


def insert_reminder(
    user_id: str,
    base_event_date: datetime,
    next_trigger_time: datetime,
    subject_person_id: Optional[str] = None,
    reminder_text_template: Optional[str] = None,
    advance_notice_config: Optional[Dict] = None,
    recurrence_rule: Optional[str] = None,
    status: str = 'active',
    display_text: Optional[str] = None,  # 新增
) -> Optional[int]:
    """
    向 reminders 表中插入一条新记录 (上传数据)。
    成功则返回新记录的 reminder_id，失败则返回 None.
    """
    import json

    # 将 JSON 字段转换为字符串
    advance_notice_json = None
    if advance_notice_config is not None:
        advance_notice_json = json.dumps(advance_notice_config)

    ins = reminders_table.insert().values(
        user_id=user_id,
        subject_person_id=subject_person_id,
        reminder_text_template=reminder_text_template,
        base_event_date=base_event_date,
        advance_notice_config=advance_notice_json,  # 传递 JSON 字符串
        recurrence_rule=recurrence_rule,
        next_trigger_time=next_trigger_time,
        status=status,
        display_text=display_text,  # 新增
    )
    try:
        last_id = sql_client.insert_return_id(ins)
        logger.info(f"成功插入提醒记录，ID: {last_id}")
        return last_id
    except Exception as e:
        logger.error(f"插入提醒记录失败: {e}")
        return None

def query_reminder_by_id(reminder_id: int, user_id: str) -> Optional[Dict]:
    """按主键 (reminder_id) 和用户ID搜索，确保用户隔离。"""
    stmt = select(reminders_table).where(
        and_(
            reminders_table.c.reminder_id == reminder_id,
            reminders_table.c.user_id == user_id
        )
    )
    try:
        return sql_client.select_one(stmt)
    except Exception as e:
        logger.error(f"按ID查询提醒失败 (ID: {reminder_id}, UserID: {user_id}): {e}")
        return None

def query_reminders_by_user(user_id: str, subject_person_id: Optional[str] = None) -> List[Dict]:
    """按索引键 (user_id, subject_person_id) 搜索。"""
    conds = [reminders_table.c.user_id == user_id]
    if subject_person_id:
        conds.append(reminders_table.c.subject_person_id == subject_person_id)

    stmt = select(reminders_table).where(and_(*conds))
    try:
        return sql_client.select_many(stmt)
    except Exception as e:
        logger.error(f"按用户查询提醒失败 (UserID: {user_id}): {e}")
        return []

def query_reminders_by_status_and_time(status: str, before_time: datetime) -> List[Dict]:
    """按复合索引键 (status, next_trigger_time) 搜索。"""
    conds = [
        reminders_table.c.status == status,
        reminders_table.c.next_trigger_time <= before_time
    ]
    stmt = select(reminders_table).where(and_(*conds)).order_by(reminders_table.c.next_trigger_time)
    try:
        return sql_client.select_many(stmt)
    except Exception as e:
        logger.error(f"按状态和时间查询提醒失败 (Status: {status}): {e}")
        return []

def update_reminder_status(reminder_id: int, user_id: str, status: str) -> bool:
    """更新指定提醒的状态，确保用户隔离。"""
    stmt = (
        reminders_table.update()
        .where(
            and_(
                reminders_table.c.reminder_id == reminder_id,
                reminders_table.c.user_id == user_id
            )
        )
        .values(status=status)
    )
    try:
        result = sql_client.update(stmt)
        affected_rows = result.rowcount if result else 0
        if affected_rows > 0:
            logger.info(f"成功更新提醒状态 (ID: {reminder_id}, UserID: {user_id}, Status: {status})")
            return True
        else:
            logger.warning(f"未找到要更新的提醒记录或状态未变化 (ID: {reminder_id}, UserID: {user_id})")
            return False
    except Exception as e:
        logger.error(f"更新提醒状态失败 (ID: {reminder_id}, UserID: {user_id}): {e}")
        return False

def delete_reminder_by_id(reminder_id: int, user_id: str) -> bool:
    """按主键 (reminder_id) 和用户ID删除记录，确保用户隔离。"""
    stmt = reminders_table.delete().where(
        and_(
            reminders_table.c.reminder_id == reminder_id,
            reminders_table.c.user_id == user_id
        )
    )
    try:
        result = sql_client.update(stmt)
        affected_rows = result.rowcount if result else 0
        if affected_rows > 0:
            logger.info(f"成功删除提醒记录 (ID: {reminder_id}, UserID: {user_id})")
            return True
        else:
            logger.warning(f"未找到要删除的提醒记录 (ID: {reminder_id}, UserID: {user_id})")
            return False
    except Exception as e:
        logger.error(f"删除提醒记录失败 (ID: {reminder_id}, UserID: {user_id}): {e}")
        return False

def update_reminder(
    reminder_id: int,
    user_id: str,
    **kwargs
) -> bool:
    """
    更新指定提醒的字段，确保用户隔离。
    kwargs 中包含要更新的字段和值
    """
    # 过滤出有效的字段
    valid_fields = {
        'base_event_date', 'next_trigger_time', 'subject_person_id',
        'reminder_text_template', 'advance_notice_config', 'recurrence_rule', 'status',
        'display_text',  # 新增
    }

    update_values = {}
    for key, value in kwargs.items():
        if key in valid_fields and value is not None:
            update_values[key] = value

    if not update_values:
        logger.warning(f"没有有效的更新字段 (ID: {reminder_id}, UserID: {user_id})")
        return False

    stmt = (
        reminders_table.update()
        .where(
            and_(
                reminders_table.c.reminder_id == reminder_id,
                reminders_table.c.user_id == user_id
            )
        )
        .values(**update_values)
    )

    try:
        result = sql_client.update(stmt)
        affected_rows = result.rowcount if result else 0
        if affected_rows > 0:
            logger.info(f"成功更新提醒 (ID: {reminder_id}, UserID: {user_id})")
            return True
        else:
            logger.warning(f"未找到要更新的提醒记录 (ID: {reminder_id}, UserID: {user_id})")
            return False
    except Exception as e:
        logger.error(f"更新提醒失败 (ID: {reminder_id}, UserID: {user_id}): {e}")
        return False

