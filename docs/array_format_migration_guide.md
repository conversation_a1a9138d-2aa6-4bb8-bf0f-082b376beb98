# 数组格式迁移指南

## 概述

为了解决多值字段格式化问题，我们将数据结构从管道符分隔的字符串格式迁移到数组格式。这样可以：

1. **避免格式化错误**：不再需要AI判断是否分割文本
2. **数据结构清晰**：数组天然支持多值
3. **前端处理简单**：直接遍历数组即可
4. **支持复杂内容**：每个数组元素可以是任意复杂的文本

## 数据格式对比

### 旧格式（管道符分隔）
```json
{
  "旅游历史": "去北京旅游了|挺好",
  "餐饮偏好": "川菜|粤菜|湘菜",
  "兴趣爱好": "篮球|足球|游泳"
}
```

### 新格式（数组）
```json
{
  "旅游历史": ["去北京旅游了，挺好"],
  "餐饮偏好": ["川菜", "粤菜", "湘菜"],
  "兴趣爱好": ["篮球", "足球", "游泳"]
}
```

## 迁移步骤

### 1. 测试新格式化逻辑

```bash
python test_array_format.py
```

### 2. 试运行数据迁移

```bash
# 查看需要迁移的数据
python migrate_to_array_format.py zhangzheng51
```

### 3. 执行实际迁移

```bash
# 实际执行迁移（谨慎操作）
python migrate_to_array_format.py zhangzheng51 --execute
```

## 迁移逻辑

### 自动转换规则

1. **管道符数据**：`"川菜|粤菜|湘菜"` → `["川菜", "粤菜", "湘菜"]`

2. **列表分隔符数据**：
   - `"川菜，粤菜，湘菜"` → `["川菜", "粤菜", "湘菜"]`
   - `"川菜；粤菜；湘菜"` → `["川菜", "粤菜", "湘菜"]`
   - `"北京 上海 广州"` → `["北京", "上海", "广州"]`

3. **描述性文本**：`"去北京旅游了，挺好"` → `["去北京旅游了，挺好"]`

4. **已有数组**：保持不变

5. **空值**：转换为空数组 `[]`

### 判断逻辑

- **包含管道符**：直接按管道符分割
- **包含分隔符但无描述性词汇**：按分隔符分割
- **包含描述性词汇**：作为单个元素
- **其他情况**：作为单个元素

## 新的数据处理流程

### 写入时
```python
# 新数据直接使用数组格式
key_attributes = {
    "旅游历史": ["去北京旅游了，挺好"],
    "兴趣爱好": ["篮球", "足球", "游泳"]
}
```

### 读取时
```python
# 前端直接处理数组
for item in person["key_attributes"]["旅游历史"]:
    print(item)
```

## 受影响的字段

以下字段会被转换为数组格式：

- `旅游历史`
- `餐饮偏好`
- `兴趣爱好`
- `技能特长`
- `工作经历`
- `教育背景`
- 其他长度 ≥ 3 的非单值字段

### 不受影响的字段

- `性别`、`年龄`、`职业`、`关系`、`当前城市`、`家乡`
- `基本信息` 嵌套结构
- 长度 < 3 的字段

## 前端适配

### 显示多值字段
```javascript
// 旧方式
const items = person.key_attributes.旅游历史.split('|');

// 新方式
const items = person.key_attributes.旅游历史; // 直接是数组
```

### 编辑多值字段
```javascript
// 新增项目
person.key_attributes.旅游历史.push("新的旅游经历");

// 删除项目
person.key_attributes.旅游历史.splice(index, 1);
```

## 注意事项

1. **备份数据**：迁移前建议备份数据库
2. **分批迁移**：可以按用户分批进行迁移
3. **验证结果**：迁移后验证数据正确性
4. **前端同步**：确保前端代码同步更新

## 回滚方案

如果需要回滚到旧格式：

```python
# 将数组转换回管道符格式
def array_to_pipe_format(value):
    if isinstance(value, list):
        return "|".join(str(item) for item in value)
    return value
```

## 测试验证

### 单元测试
```bash
python test_array_format.py
```

### 集成测试
1. 创建测试数据
2. 执行迁移
3. 验证结果
4. 测试前端显示

## 常见问题

### Q: 迁移会丢失数据吗？
A: 不会，迁移脚本会保留所有原始内容，只是改变存储格式。

### Q: 如何处理特殊字符？
A: 数组格式天然支持特殊字符，不需要转义。

### Q: 性能影响如何？
A: JSON数组的存储和查询性能与字符串相当，前端处理更简单。

### Q: 可以部分迁移吗？
A: 可以，迁移脚本支持按用户或按字段进行部分迁移。
