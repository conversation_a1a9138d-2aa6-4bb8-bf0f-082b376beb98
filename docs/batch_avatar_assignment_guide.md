# 批量头像分配接口使用指南

## 概述

批量头像分配接口用于为历史数据中没有头像的人员自动分配合适的头像。该接口支持试运行模式和实际执行模式，可以安全地预览分配结果后再执行实际更新。

## 代码结构

- **接口层**: `app/app.py` - 提供 HTTP 接口
- **服务层**: `app/service/batch_avatar_service.py` - 核心业务逻辑
- **头像分配**: `app/service/avatar_service.py` - 智能头像分配逻辑

## 接口信息

- **接口路径**: `POST /humanrelation/batch_assign_avatars`
- **请求方法**: POST
- **内容类型**: application/json

## 请求参数

请求体为 JSON 格式，包含以下字段：

| 参数名  | 类型    | 必填 | 默认值 | 说明                                        |
| ------- | ------- | ---- | ------ | ------------------------------------------- |
| user_id | string  | 是   | -      | 用户 ID，如果为空字符串则处理所有用户的数据 |
| limit   | integer | 否   | 100    | 每次处理的最大数量                          |
| dry_run | boolean | 否   | true   | 是否为试运行模式（只预览不实际更新）        |

## 使用示例

### 1. 试运行模式（推荐先使用）

预览指定用户的头像分配结果：

```bash
curl -X POST "http://localhost:8000/humanrelation/batch_assign_avatars" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "zhangzheng51",
    "limit": 50,
    "dry_run": true
  }'
```

### 2. 实际执行模式

确认预览结果无误后，执行实际更新：

```bash
curl -X POST "http://localhost:8000/humanrelation/batch_assign_avatars" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "zhangzheng51",
    "limit": 50,
    "dry_run": false
  }'
```

### 3. 处理所有用户数据

```bash
curl -X POST "http://localhost:8000/humanrelation/batch_assign_avatars" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "",
    "limit": 100,
    "dry_run": true
  }'
```

## 响应格式

### 成功响应

```json
{
  "result": "success",
  "message": "批量头像分配完成 (试运行)",
  "processed_count": 25,
  "updated_count": 0,
  "failed_count": 0,
  "dry_run": true,
  "details": [
    {
      "person_id": "abc-123",
      "canonical_name": "妈妈",
      "old_avatar": "",
      "new_avatar": "https://example.com/avatar/mom1.jpg",
      "status": "preview"
    },
    {
      "person_id": "def-456",
      "canonical_name": "李明",
      "old_avatar": "https://via.placeholder.com/150x150/cccccc/666666?text=Avatar",
      "new_avatar": "https://example.com/avatar/young_male2.jpg",
      "status": "preview"
    }
  ],
  "total_results": 25,
  "showing_first": 20
}
```

### 错误响应

```json
{
  "result": "error",
  "reason": "获取人员列表失败"
}
```

## 响应字段说明

| 字段名          | 类型    | 说明                                 |
| --------------- | ------- | ------------------------------------ |
| result          | string  | 操作结果：success 或 error           |
| message         | string  | 操作描述信息                         |
| processed_count | integer | 处理的人员总数                       |
| updated_count   | integer | 实际更新成功的人员数（试运行时为 0） |
| failed_count    | integer | 处理失败的人员数                     |
| dry_run         | boolean | 是否为试运行模式                     |
| details         | array   | 详细处理结果（最多显示前 20 条）     |
| total_results   | integer | 总结果数（当结果超过 20 条时显示）   |
| showing_first   | integer | 显示的前 N 条结果数                  |

### details 数组中的对象字段

| 字段名         | 类型   | 说明                                                                      |
| -------------- | ------ | ------------------------------------------------------------------------- |
| person_id      | string | 人员 ID                                                                   |
| canonical_name | string | 人员姓名                                                                  |
| old_avatar     | string | 原头像 URL                                                                |
| new_avatar     | string | 新分配的头像 URL                                                          |
| status         | string | 处理状态：preview（预览）、success（成功）、failed（失败）、error（错误） |
| error          | string | 错误信息（仅在失败时显示）                                                |

## 头像分配逻辑

系统会根据以下优先级为人员分配头像：

1. **姓名识别**：从姓名中识别角色关键词（如"妈妈"、"爸爸"等）
2. **关系识别**：从人际关系中识别角色信息
3. **属性识别**：从关键属性中识别角色相关信息
4. **性别细化**：结合性别信息细化角色（如"青年男"、"青年女"）
5. **默认头像**：无法识别时使用默认头像

## 注意事项

1. **建议先试运行**：首次使用时建议先使用 `dry_run: true` 预览结果
2. **分批处理**：对于大量数据，建议分批处理，避免一次性处理过多数据
3. **检查结果**：实际执行后建议检查分配结果是否符合预期
4. **头像资源**：确保 Lion 配置中心已配置好头像资源池
5. **权限控制**：该接口会修改数据库，请谨慎使用

## 常见问题

### Q: 为什么有些人员没有被处理？

A: 接口只处理没有头像或使用默认占位符头像的人员。已有有效头像的人员会被跳过。

### Q: 如何查看头像资源池配置？

A: 可以调用 `GET /humanrelation/avatar_pools` 接口查看当前的头像资源池配置。

### Q: 分配的头像不合适怎么办？

A: 可以通过人员档案更新接口手动修改头像，或者优化 Lion 配置中心的头像资源池配置。

### Q: 可以重复执行吗？

A: 可以，但已有头像的人员不会被重复处理。如需重新分配，需要先清空对应人员的头像字段。
